import React from 'react';
import { Button, type ButtonProps } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

interface FormButtonProps extends Omit<ButtonProps, 'children'> {
  text?: string;
  children?: React.ReactNode;
}

const FormButton: React.FC<FormButtonProps> = ({
  text,
  children,
  className = '',
  type = 'primary',
  htmlType = 'submit',
  size = 'large',
  ...restProps
}) => {
  const { t } = useLanguage();

  // 合并默认样式和传入的className
  const mergedClassName = `[&.ant-btn-variant-solid:disabled]:opacity-45 h-[63px] w-full ${className}`;

  return (
    <Button
      type={type}
      htmlType={htmlType}
      size={size}
      className={mergedClassName}
      {...restProps}
    >
      {children || text || t('auth.register.step4.form.signUp')}
    </Button>
  );
};

export default FormButton;
